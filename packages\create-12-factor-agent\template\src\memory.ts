// Memory management service following 12-factor agents principles
// Factor 5: Unify execution state and business state

import crypto from 'crypto';
import fs from 'fs/promises';
import path from 'path';

export interface Memory {
    id: string;
    content: string;
    type: 'fact' | 'preference' | 'context' | 'instruction';
    importance_level: number;
    tags: string[];
    created_at: Date;
    updated_at: Date;
    thread_id?: string;
}

export interface MemorySearchResult {
    memory: Memory;
    relevance_score: number;
}

export class MemoryStore {
    private memoriesDir: string;
    
    constructor() {
        this.memoriesDir = path.join(process.cwd(), '.memories');
    }
    
    async saveMemory(
        content: string, 
        type: Memory['type'], 
        importance_level: number, 
        tags: string[] = [],
        thread_id?: string
    ): Promise<string> {
        await fs.mkdir(this.memoriesDir, { recursive: true });
        
        const memory: Memory = {
            id: crypto.randomUUID(),
            content,
            type,
            importance_level,
            tags,
            created_at: new Date(),
            updated_at: new Date(),
            thread_id
        };
        
        const filePath = path.join(this.memoriesDir, `${memory.id}.json`);
        await fs.writeFile(filePath, JSON.stringify(memory, null, 2));
        
        console.log(`Memory saved: ${memory.id} - ${content.substring(0, 50)}...`);
        return memory.id;
    }
    
    async retrieveMemories(
        query: string, 
        type?: Memory['type'], 
        minImportance?: number
    ): Promise<MemorySearchResult[]> {
        try {
            await fs.mkdir(this.memoriesDir, { recursive: true });
            const files = await fs.readdir(this.memoriesDir);
            const memories: Memory[] = [];
            
            for (const file of files) {
                if (file.endsWith('.json')) {
                    const filePath = path.join(this.memoriesDir, file);
                    const data = await fs.readFile(filePath, 'utf8');
                    const memory: Memory = JSON.parse(data);
                    memories.push(memory);
                }
            }
            
            // Filter by type and importance if specified
            let filteredMemories = memories;
            if (type) {
                filteredMemories = filteredMemories.filter(m => m.type === type);
            }
            if (minImportance) {
                filteredMemories = filteredMemories.filter(m => m.importance_level >= minImportance);
            }
            
            // Simple text-based relevance scoring
            const results: MemorySearchResult[] = filteredMemories.map(memory => {
                const relevance_score = this.calculateRelevance(query, memory);
                return { memory, relevance_score };
            });
            
            // Sort by relevance and importance
            results.sort((a, b) => {
                const scoreA = a.relevance_score * a.memory.importance_level;
                const scoreB = b.relevance_score * b.memory.importance_level;
                return scoreB - scoreA;
            });
            
            return results.slice(0, 10); // Return top 10 results
        } catch (error) {
            console.error('Error retrieving memories:', error);
            return [];
        }
    }
    
    async updateMemory(
        id: string, 
        newContent?: string, 
        newImportanceLevel?: number, 
        additionalTags?: string[]
    ): Promise<boolean> {
        try {
            const filePath = path.join(this.memoriesDir, `${id}.json`);
            const data = await fs.readFile(filePath, 'utf8');
            const memory: Memory = JSON.parse(data);
            
            if (newContent) memory.content = newContent;
            if (newImportanceLevel) memory.importance_level = newImportanceLevel;
            if (additionalTags) memory.tags = [...memory.tags, ...additionalTags];
            memory.updated_at = new Date();
            
            await fs.writeFile(filePath, JSON.stringify(memory, null, 2));
            console.log(`Memory updated: ${id}`);
            return true;
        } catch (error) {
            console.error('Error updating memory:', error);
            return false;
        }
    }
    
    async deleteMemory(id: string): Promise<boolean> {
        try {
            const filePath = path.join(this.memoriesDir, `${id}.json`);
            await fs.unlink(filePath);
            console.log(`Memory deleted: ${id}`);
            return true;
        } catch (error) {
            console.error('Error deleting memory:', error);
            return false;
        }
    }
    
    private calculateRelevance(query: string, memory: Memory): number {
        const queryLower = query.toLowerCase();
        const contentLower = memory.content.toLowerCase();
        const tagsLower = memory.tags.map(tag => tag.toLowerCase());
        
        let score = 0;
        
        // Exact phrase match
        if (contentLower.includes(queryLower)) {
            score += 10;
        }
        
        // Word matches
        const queryWords = queryLower.split(/\s+/);
        const contentWords = contentLower.split(/\s+/);
        
        for (const queryWord of queryWords) {
            if (contentWords.includes(queryWord)) {
                score += 2;
            }
            // Tag matches
            if (tagsLower.includes(queryWord)) {
                score += 3;
            }
        }
        
        return score;
    }
}
