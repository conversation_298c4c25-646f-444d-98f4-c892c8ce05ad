[← 返回 README](../README_zh.md)

### 1. 自然语言到工具调用

代理构建中最常见的模式之一是将自然语言转换为结构化工具调用。这是一个强大的模式，允许你构建能够推理任务并执行它们的代理。

![110-natural-language-tool-calls](../img/110-natural-language-tool-calls.png)

当原子化应用时，这种模式是将这样的短语进行简单转换

> 你能为Terri创建一个750美元的付款链接，用于赞助二月份的AI修补者聚会吗？

转换为描述Stripe API调用的结构化对象，如

```json
{
  "function": {
    "name": "create_payment_link",
    "parameters": {
      "amount": 750,
      "customer": "cust_128934ddasf9",
      "product": "prod_8675309",
      "price": "prc_09874329fds",
      "quantity": 1,
      "memo": "嗨Jeff - 请看下面二月份AI修补者聚会的付款链接"
    }
  }
}
```

**注意**：实际上stripe API更复杂一些，[真正做这件事的代理](https://github.com/dexhorthy/mailcrew)（[视频](https://www.youtube.com/watch?v=f_cKnoPC_Oo)）会列出客户、列出产品、列出价格等，以使用正确的id构建此有效负载，或在提示/上下文窗口中包含这些id（我们将在下面看到它们在某种程度上是同一件事！）

从那里，确定性代码可以接收有效负载并对其进行处理。（更多内容请参见[因子3](./factor-03-own-your-context-window.md)）

```python
# LLM接受自然语言并返回结构化对象
nextStep = await llm.determineNextStep(
  """
  为Jeff创建一个750美元的付款链接
  用于赞助二月份的AI修补者聚会
  """
  )

# 根据其函数处理结构化输出
if nextStep.function == 'create_payment_link':
    stripe.paymentlinks.create(nextStep.parameters)
    return  # 或者你想要的任何东西，见下文
elif nextStep.function == 'something_else':
    # ... 更多情况
    pass
else:  # 模型没有调用我们知道的工具
    # 做其他事情
    pass
```

**注意**：虽然完整的代理会接收API调用结果并与其循环，最终返回类似这样的内容

> 我已经成功为Terri创建了一个750美元的付款链接，用于赞助二月份的AI修补者聚会。这是链接：https://buy.stripe.com/test_1234567890

**相反**，我们实际上要在这里跳过那一步，并将其保存为另一个因子，你可能想要也可能不想要也合并（由你决定！）

[← 我们如何来到这里](./brief-history-of-software.md) | [拥有你的提示 →](./factor-02-own-your-prompts.md)
