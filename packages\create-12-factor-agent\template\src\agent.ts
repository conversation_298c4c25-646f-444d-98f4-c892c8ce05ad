import {
    AddTool,
    SubtractTool,
    DivideTool,
    MultiplyTool,
    SaveMemoryTool,
    RetrieveMemoryTool,
    UpdateMemoryTool,
    DeleteMemoryTool,
    b,
} from "../baml_client";
import { MemoryStore } from "./memory";

export interface Event {
    type: string;
    data: any;
}

export class Thread {
    events: Event[] = [];

    constructor(events: Event[]) {
        this.events = events;
    }

    serializeForLLM() {
        return this.events.map((e) => this.serializeOneEvent(e)).join("\n");
    }

    trimLeadingWhitespace(s: string) {
        return s.replace(/^[ \t]+/gm, "");
    }

    serializeOneEvent(e: Event) {
        return this.trimLeadingWhitespace(`
            <${e.data?.intent || e.type}>
            ${
                typeof e.data !== "object"
                    ? e.data
                    : Object.keys(e.data)
                          .filter((k) => k !== "intent")
                          .map((k) => `${k}: ${e.data[k]}`)
                          .join("\n")
            }
            </${e.data?.intent || e.type}>
        `);
    }

    awaitingHumanResponse(): boolean {
        const lastEvent = this.events[this.events.length - 1];
        return ["request_more_information", "done_for_now"].includes(
            lastEvent.data.intent
        );
    }

    awaitingHumanApproval(): boolean {
        const lastEvent = this.events[this.events.length - 1];
        return lastEvent.data.intent === "divide";
    }

    lastEvent(): Event {
        return this.events[this.events.length - 1];
    }
}

export type CalculatorTool = AddTool | SubtractTool | MultiplyTool | DivideTool;
export type MemoryTool =
    | SaveMemoryTool
    | RetrieveMemoryTool
    | UpdateMemoryTool
    | DeleteMemoryTool;

// Initialize memory store
const memoryStore = new MemoryStore();

export async function handleMemoryStep(
    nextStep: MemoryTool,
    thread: Thread
): Promise<Thread> {
    let result: any;
    switch (nextStep.intent) {
        case "save_memory":
            result = await memoryStore.saveMemory(
                nextStep.memory_content,
                nextStep.memory_type as any,
                nextStep.importance_level,
                nextStep.tags
            );
            console.log("memory_saved", result);
            thread.events.push({
                type: "tool_response",
                data: `Memory saved with ID: ${result}`,
            });
            return thread;
        case "retrieve_memory":
            result = await memoryStore.retrieveMemories(
                nextStep.query,
                nextStep.memory_type as any,
                nextStep.min_importance
            );
            console.log("memories_retrieved", result.length);
            thread.events.push({
                type: "tool_response",
                data:
                    result.length > 0
                        ? `Found ${result.length} relevant memories: ${result
                              .map((r) => r.memory.content)
                              .join("; ")}`
                        : "No relevant memories found",
            });
            return thread;
        case "update_memory":
            result = await memoryStore.updateMemory(
                nextStep.memory_id,
                nextStep.new_content,
                nextStep.new_importance_level,
                nextStep.additional_tags
            );
            console.log("memory_updated", result);
            thread.events.push({
                type: "tool_response",
                data: result
                    ? "Memory updated successfully"
                    : "Failed to update memory",
            });
            return thread;
        case "delete_memory":
            result = await memoryStore.deleteMemory(nextStep.memory_id);
            console.log("memory_deleted", result);
            thread.events.push({
                type: "tool_response",
                data: result
                    ? `Memory deleted: ${nextStep.reason}`
                    : "Failed to delete memory",
            });
            return thread;
    }
}

export async function handleNextStep(
    nextStep: CalculatorTool,
    thread: Thread
): Promise<Thread> {
    let result: number;
    switch (nextStep.intent) {
        case "add":
            result = nextStep.a + nextStep.b;
            console.log("tool_response", result);
            thread.events.push({
                type: "tool_response",
                data: result,
            });
            return thread;
        case "subtract":
            result = nextStep.a - nextStep.b;
            console.log("tool_response", result);
            thread.events.push({
                type: "tool_response",
                data: result,
            });
            return thread;
        case "multiply":
            result = nextStep.a * nextStep.b;
            console.log("tool_response", result);
            thread.events.push({
                type: "tool_response",
                data: result,
            });
            return thread;
        case "divide":
            result = nextStep.a / nextStep.b;
            console.log("tool_response", result);
            thread.events.push({
                type: "tool_response",
                data: result,
            });
            return thread;
    }
}

export async function agentLoop(thread: Thread): Promise<Thread> {
    while (true) {
        const nextStep = await b.DetermineNextStep(thread.serializeForLLM());
        console.log("nextStep", nextStep);

        thread.events.push({
            type: "tool_call",
            data: nextStep,
        });

        switch (nextStep.intent) {
            case "done_for_now":
            case "request_more_information":
            case "request_approval_from_manager":
                // response to human, return the thread
                return thread;
            case "divide":
                // divide is scary, return it for human approval
                return thread;
            case "add":
            case "subtract":
            case "multiply":
                thread = await handleNextStep(nextStep, thread);
                break;
            case "save_memory":
            case "retrieve_memory":
            case "update_memory":
            case "delete_memory":
                thread = await handleMemoryStep(nextStep, thread);
                break;
        }
    }
}
