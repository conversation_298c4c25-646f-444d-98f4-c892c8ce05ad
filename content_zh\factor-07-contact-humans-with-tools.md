[← 返回 README](../README_zh.md)

### 7. 通过工具调用联系人类

默认情况下，LLM API依赖于一个基本的高风险令牌选择：我们是返回纯文本内容，还是返回结构化数据？

![170-contact-humans-with-tools](../img/170-contact-humans-with-tools.png)

你在第一个令牌的选择上投入了很多权重，在`东京的天气`情况下，它是

> "东京"

但在`fetch_weather`情况下，它是一些特殊令牌来表示JSON对象的开始。

> |JSON>

你可能通过让LLM*总是*输出json，然后用一些自然语言令牌如`request_human_input`或`done_for_now`声明其意图（而不是像`check_weather_in_city`这样的"适当"工具）来获得更好的结果。

再次，你可能不会从中获得任何性能提升，但你应该实验，并确保你可以自由尝试奇怪的东西来获得最佳结果。

```python

class Options:
  urgency: Literal["low", "medium", "high"]
  format: Literal["free_text", "yes_no", "multiple_choice"]
  choices: List[str]

# 人类交互的工具定义
class RequestHumanInput:
  intent: "request_human_input"
  question: str
  context: str
  options: Options

# 在代理循环中的示例用法
if nextStep.intent == 'request_human_input':
  thread.events.append({
    type: 'human_input_requested',
    data: nextStep
  })
  thread_id = await save_state(thread)
  await notify_human(nextStep, thread_id)
  return # 中断循环并等待响应带着线程ID回来
else:
  # ... 其他情况
```

稍后，你可能会从处理slack、电子邮件、短信或其他事件的系统接收webhook。

```python

@app.post('/webhook')
def webhook(req: Request):
  thread_id = req.body.threadId
  thread = await load_state(thread_id)
  thread.events.push({
    type: 'response_from_human',
    data: req.body
  })
  # ... 为简洁起见简化，你可能不想在这里阻塞web worker
  next_step = await determine_next_step(thread_to_prompt(thread))
  thread.events.append(next_step)
  result = await handle_next_step(thread, next_step)
  # todo - 循环或中断或你想要的任何东西

  return {"status": "ok"}
```

上面包括来自[因子5 - 统一执行状态和业务状态](./factor-05-unify-execution-state.md)、[因子8 - 拥有你的控制流](./factor-08-own-your-control-flow.md)、[因子3 - 拥有你的上下文窗口](./factor-03-own-your-context-window.md)和[因子4 - 工具只是结构化输出](./factor-04-tools-are-structured-outputs.md)以及其他几个的模式。

如果我们使用来自[因子3 - 拥有你的上下文窗口](./factor-03-own-your-context-window.md)的XML格式，我们在几轮后的上下文窗口可能看起来像这样：

```xml

(为简洁起见省略)

<slack_message>
    From: @alex
    Channel: #deployments
    Text: 你能将后端v1.2.3部署到生产环境吗？
    Thread: []
</slack_message>

<request_human_input>
    intent: "request_human_input"
    question: "你想继续将v1.2.3部署到生产环境吗？"
    context: "这是一个会影响实时用户的生产部署。"
    options: {
        urgency: "high"
        format: "yes_no"
    }
</request_human_input>

<human_response>
    response: "是的，请继续"
    approved: true
    timestamp: "2024-03-15T10:30:00Z"
    user: "<EMAIL>"
</human_response>

<deploy_backend>
    intent: "deploy_backend"
    tag: "v1.2.3"
    environment: "production"
</deploy_backend>

<deploy_backend_result>
    status: "success"
    message: "部署v1.2.3到生产环境成功完成。"
    timestamp: "2024-03-15T10:30:00Z"
</deploy_backend_result>
```

好处：

1. **清晰指令**：不同类型人类联系的工具允许LLM更具体
2. **内循环vs外循环**：启用传统chatGPT风格界面**之外**的代理工作流，其中控制流和上下文初始化可能是`代理->人类`而不是`人类->代理`（想想，由cron或事件启动的代理）
3. **多人类访问**：可以通过结构化事件轻松跟踪和协调来自不同人类的输入
4. **多代理**：简单抽象可以轻松扩展以支持`代理->代理`请求和响应
5. **持久性**：与[因子6 - 通过简单API启动/暂停/恢复](./factor-06-launch-pause-resume.md)结合，这使得持久、可靠和可内省的多人工作流成为可能

[更多关于外循环代理在这里](https://theouterloop.substack.com/p/openais-realtime-api-is-a-step-towards)

![175-outer-loop-agents](../img/175-outer-loop-agents.png)

与[因子11 - 从任何地方触发，在用户所在的地方遇见他们](./factor-11-trigger-from-anywhere.md)配合得很好

[← 启动/暂停/恢复](./factor-06-launch-pause-resume.md) | [拥有你的控制流 →](./factor-08-own-your-control-flow.md)
