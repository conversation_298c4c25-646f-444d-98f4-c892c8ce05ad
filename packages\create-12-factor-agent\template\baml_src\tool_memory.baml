// Memory management tools following 12-factor agents principles

class SaveMemoryTool {
    intent "save_memory"
    memory_content string @description("The important information to remember for future conversations")
    memory_type string @description("Type of memory: 'fact', 'preference', 'context', 'instruction'")
    importance_level int @description("Importance level from 1-10, where 10 is most important")
    tags string[] @description("Optional tags to categorize the memory")
}

class RetrieveMemoryTool {
    intent "retrieve_memory"
    query string @description("Search query to find relevant memories")
    memory_type string? @description("Optional filter by memory type")
    min_importance int? @description("Optional minimum importance level filter")
}

class UpdateMemoryTool {
    intent "update_memory"
    memory_id string @description("ID of the memory to update")
    new_content string @description("Updated memory content")
    new_importance_level int? @description("Optional updated importance level")
    additional_tags string[]? @description("Optional additional tags to add")
}

class DeleteMemoryTool {
    intent "delete_memory"
    memory_id string @description("ID of the memory to delete")
    reason string @description("Reason for deletion")
}

// Union type for all memory tools
type MemoryTools = SaveMemoryTool | RetrieveMemoryTool | UpdateMemoryTool | DeleteMemoryTool
