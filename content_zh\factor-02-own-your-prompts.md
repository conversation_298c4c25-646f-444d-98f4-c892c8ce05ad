[← 返回 README](../README_zh.md)

### 2. 拥有你的提示

不要将你的提示工程外包给框架。

![120-own-your-prompts](../img/120-own-your-prompts.png)

顺便说一下，[这远非新颖的建议：](https://hamel.dev/blog/posts/prompt/)

![image](https://github.com/user-attachments/assets/575bab37-0f96-49fb-9ce3-9a883cdd420b)

一些框架提供这样的"黑盒"方法：

```python
agent = Agent(
  role="...",
  goal="...",
  personality="...",
  tools=[tool1, tool2, tool3]
)

task = Task(
  instructions="...",
  expected_output=OutputModel
)

result = agent.run(task)
```

这对于引入一些顶级提示工程来帮助你开始很棒，但通常很难调整和/或逆向工程以获得正确的令牌到你的模型中。

相反，拥有你的提示并将它们视为一等代码：

```rust
function DetermineNextStep(thread: string) -> DoneForNow | ListGitTags | DeployBackend | DeployFrontend | RequestMoreInformation {
  prompt #"
    {{ _.role("system") }}
    
    你是一个有用的助手，管理前端和后端系统的部署。
    你勤奋工作，通过遵循最佳实践和适当的部署程序
    来确保安全和成功的部署。
    
    在部署任何系统之前，你应该检查：
    - 部署环境（暂存 vs 生产）
    - 要部署的正确标签/版本
    - 当前系统状态
    
    你可以使用deploy_backend、deploy_frontend和check_deployment_status等工具
    来管理部署。对于敏感部署，使用request_approval获取
    人类验证。
    
    总是考虑首先要做什么，比如：
    - 检查当前部署状态
    - 验证部署标签存在
    - 如果需要请求批准
    - 在生产之前部署到暂存
    - 监控部署进度
    
    {{ _.role("user") }}

    {{ thread }}
    
    下一步应该是什么？
  "#
}
```

（上面的例子使用[BAML](https://github.com/boundaryml/baml)生成提示，但你可以使用任何你想要的提示工程工具，甚至只是手动模板化）

如果签名看起来有点奇怪，我们将在[因子4 - 工具只是结构化输出](./factor-04-tools-are-structured-outputs.md)中讨论

```typescript
function DetermineNextStep(thread: string) -> DoneForNow | ListGitTags | DeployBackend | DeployFrontend | RequestMoreInformation {
```

拥有你的提示的主要好处：

1. **完全控制**：编写你的代理需要的确切指令，没有黑盒抽象
2. **测试和评估**：为你的提示构建测试和评估，就像你为任何其他代码做的一样
3. **迭代**：基于现实世界的性能快速修改提示
4. **透明度**：确切知道你的代理正在使用什么指令
5. **角色黑客**：利用支持用户/助手角色非标准使用的API - 例如，现在已弃用的OpenAI"completions" API的非聊天版本。这包括一些所谓的"模型煤气灯"技术

记住：你的提示是你的应用程序逻辑和LLM之间的主要接口。

完全控制你的提示为你提供了生产级代理所需的灵活性和提示控制。

我不知道什么是最好的提示，但我知道你想要能够尝试一切的灵活性。

[← 自然语言到工具调用](./factor-01-natural-language-to-tool-calls.md) | [拥有你的上下文窗口 →](./factor-03-own-your-context-window.md)
