{"name": "my-agent", "version": "0.1.0", "private": true, "scripts": {"dev": "tsx src/index.ts", "build": "tsc"}, "dependencies": {"@boundaryml/baml": "latest", "express": "^5.1.0", "humanlayer": "^0.7.7", "tsx": "^4.15.0", "typescript": "^5.0.0", "zod": "^3.25.64"}, "devDependencies": {"@types/express": "^5.0.1", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "supertest": "^7.1.0"}}