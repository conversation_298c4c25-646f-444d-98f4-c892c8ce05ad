# 12-Factor Agents - 构建可靠 LLM 应用的原则

<div align="center">
<a href="https://www.apache.org/licenses/LICENSE-2.0">
        <img src="https://img.shields.io/badge/Code-Apache%202.0-blue.svg" alt="Code License: Apache 2.0"></a>
<a href="https://creativecommons.org/licenses/by-sa/4.0/">
        <img src="https://img.shields.io/badge/Content-CC%20BY--SA%204.0-lightgrey.svg" alt="Content License: CC BY-SA 4.0"></a>
<a href="https://humanlayer.dev/discord">
    <img src="https://img.shields.io/badge/chat-discord-5865F2" alt="Discord Server"></a>
<a href="https://www.youtube.com/watch?v=8kMaTybvDUw">
    <img src="https://img.shields.io/badge/aidotengineer-conf_talk_(17m)-white" alt="YouTube
Deep Dive"></a>
<a href="https://www.youtube.com/watch?v=yxJDyQ8v6P0">
    <img src="https://img.shields.io/badge/youtube-deep_dive-crimson" alt="YouTube
Deep Dive"></a>

<p></p>

_秉承[12 Factor Apps](https://12factor.net/)的精神_。_本项目的源代码已下载到本地，我欢迎您的反馈和贡献。让我们一起探索！_

> [!TIP]
> 错过了 AI 工程师世界博览会？[在这里观看演讲](https://www.youtube.com/watch?v=8kMaTybvDUw)
>
> 寻找上下文工程？[直接跳转到因子 3](./content/factor-03-own-your-context-window.md)
>
> 想要为 `npx/uvx create-12-factor-agent` 做贡献 - 查看[讨论线程](https://github.com/humanlayer/12-factor-agents/discussions/61)

<img referrerpolicy="no-referrer-when-downgrade" src="https://static.scarf.sh/a.png?x-pxid=2acad99a-c2d9-48df-86f5-9ca8061b7bf9" />

<a href="#visual-nav"><img width="907" alt="Screenshot 2025-04-03 at 2 49 07 PM" src="https://github.com/user-attachments/assets/23286ad8-7bef-4902-b371-88ff6a22e998" /></a>

嗨，我是 Dex。我已经[研究](https://youtu.be/8bIHcttkOTE)[AI 代理](https://theouterloop.substack.com)[一段时间了](https://humanlayer.dev)。

**我尝试过所有现有的代理框架**，从即插即用的 crew/langchains 到"极简主义"的 smolagents，再到"生产级"的 langraph、griptape 等。

**我与很多真正强大的创始人交谈过**，包括 YC 内外的，他们都在用 AI 构建真正令人印象深刻的东西。他们中的大多数都在自己构建技术栈。我在面向客户的生产代理中很少看到框架的使用。

**我惊讶地发现**，大多数自称"AI 代理"的产品实际上并不那么具有代理性。它们中的很多主要是确定性代码，在恰到好处的点上撒上 LLM 步骤，让体验变得真正神奇。

代理，至少好的代理，不遵循["这是你的提示，这是一袋工具，循环直到达到目标"](https://www.anthropic.com/engineering/building-effective-agents#agents)的模式。相反，它们主要由软件组成。

所以，我开始回答：

> ### **我们可以使用什么原则来构建真正足够好、可以交付给生产客户的 LLM 驱动软件？**

欢迎来到 12-factor agents。正如自 Daley 以来每一位芝加哥市长都在该市主要机场上一致宣传的那样，我们很高兴您来到这里。

_特别感谢 [@iantbutler01](https://github.com/iantbutler01)、[@tnm](https://github.com/tnm)、[@hellovai](https://www.github.com/hellovai)、[@stantonk](https://www.github.com/stantonk)、[@balanceiskey](https://www.github.com/balanceiskey)、[@AdjectiveAllison](https://www.github.com/AdjectiveAllison)、[@pfbyjy](https://www.github.com/pfbyjy)、[@a-churchill](https://www.github.com/a-churchill) 以及 SF MLOps 社区对本指南的早期反馈。_

## 简短版本：12 个因子

即使 LLM[继续呈指数级增长](./content/factor-10-small-focused-agents.md#what-if-llms-get-smarter)，仍有一些核心工程技术可以让 LLM 驱动的软件更可靠、更可扩展、更易于维护。

-   [我们如何来到这里：软件简史](./content_zh/brief-history-of-software.md)
-   [因子 1：自然语言到工具调用](./content_zh/factor-01-natural-language-to-tool-calls.md)
-   [因子 2：拥有你的提示](./content_zh/factor-02-own-your-prompts.md)
-   [因子 3：拥有你的上下文窗口](./content_zh/factor-03-own-your-context-window.md)
-   [因子 4：工具只是结构化输出](./content_zh/factor-04-tools-are-structured-outputs.md)
-   [因子 5：统一执行状态和业务状态](./content_zh/factor-05-unify-execution-state.md)
-   [因子 6：通过简单 API 启动/暂停/恢复](./content_zh/factor-06-launch-pause-resume.md)
-   [因子 7：通过工具调用联系人类](./content_zh/factor-07-contact-humans-with-tools.md)
-   [因子 8：拥有你的控制流](./content_zh/factor-08-own-your-control-flow.md)
-   [因子 9：将错误压缩到上下文窗口](./content_zh/factor-09-compact-errors.md)
-   [因子 10：小型、专注的代理](./content_zh/factor-10-small-focused-agents.md)
-   [因子 11：从任何地方触发，在用户所在的地方遇见他们](./content_zh/factor-11-trigger-from-anywhere.md)
-   [因子 12：让你的代理成为无状态归约器](./content_zh/factor-12-stateless-reducer.md)

### 视觉导航

|                                                                                                                        |                                                                                                      |                                                                                                           |
| ---------------------------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------- |
| [![factor 1](./img/110-natural-language-tool-calls.png)](./content_zh/factor-01-natural-language-to-tool-calls.md)     | [![factor 2](./img/120-own-your-prompts.png)](./content_zh/factor-02-own-your-prompts.md)            | [![factor 3](./img/130-own-your-context-building.png)](./content_zh/factor-03-own-your-context-window.md) |
| [![factor 4](./img/140-tools-are-just-structured-outputs.png)](./content_zh/factor-04-tools-are-structured-outputs.md) | [![factor 5](./img/150-unify-state.png)](./content_zh/factor-05-unify-execution-state.md)            | [![factor 6](./img/160-pause-resume-with-simple-apis.png)](./content_zh/factor-06-launch-pause-resume.md) |
| [![factor 7](./img/170-contact-humans-with-tools.png)](./content_zh/factor-07-contact-humans-with-tools.md)            | [![factor 8](./img/180-control-flow.png)](./content_zh/factor-08-own-your-control-flow.md)           | [![factor 9](./img/190-factor-9-errors-static.png)](./content_zh/factor-09-compact-errors.md)             |
| [![factor 10](./img/1a0-small-focused-agents.png)](./content_zh/factor-10-small-focused-agents.md)                     | [![factor 11](./img/1b0-trigger-from-anywhere.png)](./content_zh/factor-11-trigger-from-anywhere.md) | [![factor 12](./img/1c0-stateless-reducer.png)](./content_zh/factor-12-stateless-reducer.md)              |

## 我们如何来到这里

关于我的代理之旅以及是什么引导我们来到这里，更深入的探讨请查看[软件简史](./content_zh/brief-history-of-software.md) - 这里是一个快速总结：

### 代理的承诺

我们将谈论很多关于有向图(DGs)和它们的无环朋友 DAGs。我先指出...嗯...软件就是一个有向图。我们过去用流程图表示程序是有原因的。

![010-software-dag](./img/010-software-dag.png)

### 从代码到 DAGs

大约 20 年前，我们开始看到 DAG 编排器变得流行。我们谈论的是经典如[Airflow](https://airflow.apache.org/)、[Prefect](https://www.prefect.io/)，一些前辈，以及一些更新的如([dagster](https://dagster.io/)、[inggest](https://www.inngest.com/)、[windmill](https://www.windmill.dev/))。这些遵循相同的图模式，具有可观测性、模块化、重试、管理等额外好处。

![015-dag-orchestrators](./img/015-dag-orchestrators.png)

### 代理的承诺

我不是第一个[说这个的人](https://youtu.be/Dc99-zTMyMg?si=bcT0hIwWij2mR-40&t=73)，但当我开始学习代理时，我最大的收获是你可以扔掉 DAG。不是软件工程师编码每个步骤和边缘情况，你可以给代理一个目标和一组转换：

![025-agent-dag](./img/025-agent-dag.png)

让 LLM 实时做出决策来找出路径

![026-agent-dag-lines](./img/026-agent-dag-lines.png)

这里的承诺是你写更少的软件，你只是给 LLM 图的"边"，让它找出节点。你可以从错误中恢复，你可以写更少的代码，你可能会发现 LLM 找到问题的新颖解决方案。

### 代理作为循环

正如我们稍后会看到的，事实证明这并不完全有效。

让我们深入一步 - 使用代理，你有这个由 3 个步骤组成的循环：

1. LLM 确定工作流中的下一步，输出结构化 json（"工具调用"）
2. 确定性代码执行工具调用
3. 结果被附加到上下文窗口
4. 重复直到下一步被确定为"完成"

```python
initial_event = {"message": "..."}
context = [initial_event]
while True:
  next_step = await llm.determine_next_step(context)
  context.append(next_step)

  if (next_step.intent === "done"):
    return next_step.final_answer

  result = await execute_step(next_step)
  context.append(result)
```

我们的初始上下文只是起始事件（可能是用户消息，可能是 cron 触发，可能是 webhook 等），我们要求 llm 选择下一步（工具）或确定我们完成了。

这是一个多步骤的例子：

[![027-agent-loop-animation](./img/027-agent-loop-animation.gif)](./img/027-agent-loop-animation.gif)

<details>
<summary><a href="./img/027-agent-loop-animation.gif">GIF版本</a></summary>

![027-agent-loop-animation](./img/027-agent-loop-animation.gif)

</details>

## 为什么是 12-factor agents？

归根结底，这种方法并不像我们想要的那样有效。

在构建 HumanLayer 的过程中，我与至少 100 个 SaaS 构建者（主要是技术创始人）交谈过，他们希望让现有产品更具代理性。旅程通常是这样：

1. 决定要构建一个代理
2. 产品设计、UX 映射、解决什么问题
3. 想要快速行动，所以抓住$FRAMEWORK 并*开始构建*
4. 达到 70-80%的质量标准
5. 意识到 80%对大多数面向客户的功能来说不够好
6. 意识到超过 80%需要反向工程框架、提示、流程等
7. 从头开始

<details>
<summary>随机免责声明</summary>

**免责声明**：我不确定在确切的地方说这个，但这里似乎和任何地方一样好：**这绝不是对众多框架或在这些框架上工作的相当聪明的人的挖苦**。它们实现了令人难以置信的事情，加速了 AI 生态系统。

我希望这篇文章的一个结果是代理框架构建者可以从我和其他人的旅程中学习，让框架变得更好。

特别是对于想要快速行动但需要深度控制的构建者。

**免责声明 2**：我不会谈论 MCP。我相信你可以看到它适合的地方。

**免责声明 3**：我主要使用 typescript，出于[原因](https://www.linkedin.com/posts/dexterihorthy_llms-typescript-aiagents-activity-7290858296679313408-Lh9e?utm_source=share&utm_medium=member_desktop&rcm=ACoAAA4oHTkByAiD-wZjnGsMBUL_JT6nyyhOh30)，但所有这些东西在 python 或你喜欢的任何其他语言中都有效。

无论如何回到正题...

</details>

### 优秀 LLM 应用的设计模式

在深入研究数百个 AI 库并与数十位创始人合作后，我的直觉是：

1. 有一些让代理变得伟大的核心事物
2. 全押在一个框架上并构建本质上是一个绿地重写可能是反生产力的
3. 有一些让代理变得伟大的核心原则，如果你引入一个框架，你会得到大部分/全部
4. 但是，我见过的构建者将高质量 AI 软件交付给客户的最快方法是采用代理构建中的小模块化概念，并将它们整合到现有产品中
5. 这些来自代理的模块化概念可以被大多数熟练的软件工程师定义和应用，即使他们没有 AI 背景

> #### 我见过的构建者将好的 AI 软件交付给客户的最快方法是采用代理构建中的小模块化概念，并将它们整合到现有产品中

## 12 个因子（再次）

-   [我们如何来到这里：软件简史](./content/brief-history-of-software.md)
-   [因子 1：自然语言到工具调用](./content_zh/factor-01-natural-language-to-tool-calls.md)
-   [因子 2：拥有你的提示](./content/factor-02-own-your-prompts.md)
-   [因子 3：拥有你的上下文窗口](./content/factor-03-own-your-context-window.md)
-   [因子 4：工具只是结构化输出](./content/factor-04-tools-are-structured-outputs.md)
-   [因子 5：统一执行状态和业务状态](./content/factor-05-unify-execution-state.md)
-   [因子 6：通过简单 API 启动/暂停/恢复](./content/factor-06-launch-pause-resume.md)
-   [因子 7：通过工具调用联系人类](./content/factor-07-contact-humans-with-tools.md)
-   [因子 8：拥有你的控制流](./content/factor-08-own-your-control-flow.md)
-   [因子 9：将错误压缩到上下文窗口](./content/factor-09-compact-errors.md)
-   [因子 10：小型、专注的代理](./content/factor-10-small-focused-agents.md)
-   [因子 11：从任何地方触发，在用户所在的地方遇见他们](./content/factor-11-trigger-from-anywhere.md)
-   [因子 12：让你的代理成为无状态归约器](./content/factor-12-stateless-reducer.md)

## 荣誉提及/其他建议

-   [因子 13：预取你可能需要的所有上下文](./content/appendix-13-pre-fetch.md)

## 相关资源

-   为本指南做贡献[在这里](./)
-   [我在 2025 年 3 月的 Tool Use 播客节目中谈了很多这个](https://youtu.be/8bIHcttkOTE)
-   我在[The Outer Loop](https://theouterloop.substack.com)写了一些这方面的内容
-   我与[@hellovai](https://github.com/hellovai)一起做[关于最大化 LLM 性能的网络研讨会](https://github.com/hellovai/ai-that-works/tree/main)
-   我们用这种方法在[got-agents/agents](https://github.com/got-agents/agents)下构建 OSS 代理
-   我们忽略了自己的建议，构建了一个[在 kubernetes 中运行分布式代理的框架](https://github.com/humanlayer/kubechain)
-   本指南的其他链接：
    -   [12 Factor Apps](https://12factor.net)
    -   [构建有效代理(Anthropic)](https://www.anthropic.com/engineering/building-effective-agents#agents)
    -   [提示是函数](https://thedataexchange.media/baml-revolution-in-ai-engineering/)
    -   [库模式：为什么框架是邪恶的](https://tomasp.net/blog/2015/library-frameworks/)
    -   [错误的抽象](https://sandimetz.com/blog/2016/1/20/the-wrong-abstraction)
    -   [Mailcrew 代理](https://github.com/dexhorthy/mailcrew)
    -   [Mailcrew 演示视频](https://www.youtube.com/watch?v=f_cKnoPC_Oo)
    -   [Chainlit 演示](https://x.com/chainlit_io/status/1858613325921480922)
    -   [LLM 的 TypeScript](https://www.linkedin.com/posts/dexterihorthy_llms-typescript-aiagents-activity-7290858296679313408-Lh9e)
    -   [模式对齐解析](https://www.boundaryml.com/blog/schema-aligned-parsing)
    -   [函数调用 vs 结构化输出 vs JSON 模式](https://www.vellum.ai/blog/when-should-i-use-function-calling-structured-outputs-or-json-mode)
    -   [GitHub 上的 BAML](https://github.com/boundaryml/baml)
    -   [OpenAI JSON vs 函数调用](https://docs.llamaindex.ai/en/stable/examples/llm/openai_json_vs_function_calling/)
    -   [外循环代理](https://theouterloop.substack.com/p/openais-realtime-api-is-a-step-towards)
    -   [Airflow](https://airflow.apache.org/)
    -   [Prefect](https://www.prefect.io/)
    -   [Dagster](https://dagster.io/)
    -   [Inngest](https://www.inngest.com/)
    -   [Windmill](https://www.windmill.dev/)
    -   [AI 代理索引(MIT)](https://aiagentindex.mit.edu/)
    -   [NotebookLM 关于寻找模型能力边界](https://open.substack.com/pub/swyx/p/notebooklm?selection=08e1187c-cfee-4c63-93c9-71216640a5f8)

## 贡献者

感谢所有为 12-factor agents 做出贡献的人！

[<img src="https://avatars.githubusercontent.com/u/3730605?v=4&s=80" width="80px" alt="dexhorthy" />](https://github.com/dexhorthy) [<img src="https://avatars.githubusercontent.com/u/50557586?v=4&s=80" width="80px" alt="Sypherd" />](https://github.com/Sypherd) [<img src="https://avatars.githubusercontent.com/u/66259401?v=4&s=80" width="80px" alt="tofaramususa" />](https://github.com/tofaramususa) [<img src="https://avatars.githubusercontent.com/u/18105223?v=4&s=80" width="80px" alt="a-churchill" />](https://github.com/a-churchill) [<img src="https://avatars.githubusercontent.com/u/4084885?v=4&s=80" width="80px" alt="Elijas" />](https://github.com/Elijas) [<img src="https://avatars.githubusercontent.com/u/39267118?v=4&s=80" width="80px" alt="hugolmn" />](https://github.com/hugolmn) [<img src="https://avatars.githubusercontent.com/u/1882972?v=4&s=80" width="80px" alt="jeremypeters" />](https://github.com/jeremypeters)

[<img src="https://avatars.githubusercontent.com/u/380402?v=4&s=80" width="80px" alt="kndl" />](https://github.com/kndl) [<img src="https://avatars.githubusercontent.com/u/16674643?v=4&s=80" width="80px" alt="maciejkos" />](https://github.com/maciejkos) [<img src="https://avatars.githubusercontent.com/u/85041180?v=4&s=80" width="80px" alt="pfbyjy" />](https://github.com/pfbyjy) [<img src="https://avatars.githubusercontent.com/u/36044389?v=4&s=80" width="80px" alt="0xRaduan" />](https://github.com/0xRaduan) [<img src="https://avatars.githubusercontent.com/u/7169731?v=4&s=80" width="80px" alt="zyuanlim" />](https://github.com/zyuanlim) [<img src="https://avatars.githubusercontent.com/u/15862501?v=4&s=80" width="80px" alt="lombardo-chcg" />](https://github.com/lombardo-chcg) [<img src="https://avatars.githubusercontent.com/u/160066852?v=4&s=80" width="80px" alt="sahanatvessel" />](https://github.com/sahanatvessel)

## 版本

这是 12-factor agents 的当前版本，版本 1.0。在[v1.1 分支](https://github.com/humanlayer/12-factor-agents/tree/v1.1)上有版本 1.1 的草稿。有一些[跟踪 v1.1 工作的议题](https://github.com/humanlayer/12-factor-agents/issues?q=is%3Aissue%20state%3Aopen%20label%3Aversion%3A%3A1.1)。

## 许可证

所有内容和图像均根据<a href="https://creativecommons.org/licenses/by-sa/4.0/">CC BY-SA 4.0 许可证</a>授权

代码根据<a href="https://www.apache.org/licenses/LICENSE-2.0">Apache 2.0 许可证</a>授权
