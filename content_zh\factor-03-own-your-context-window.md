[← 返回 README](../README_zh.md)

### 3. 拥有你的上下文窗口

你不一定需要使用标准的基于消息的格式来向LLM传达上下文。

> #### 在任何给定时刻，你在代理中对LLM的输入是"这是到目前为止发生的事情，下一步是什么"

<!-- todo syntax highlighting -->
<!-- ![130-own-your-context-building](../img/130-own-your-context-building.png) -->

一切都是上下文工程。[LLM是无状态函数](https://thedataexchange.media/baml-revolution-in-ai-engineering/)，将输入转换为输出。要获得最佳输出，你需要给它们最佳输入。

创建出色的上下文意味着：

- 你给模型的提示和指令
- 你检索的任何文档或外部数据（例如RAG）
- 任何过去的状态、工具调用、结果或其他历史
- 来自相关但独立历史/对话的任何过去消息或事件（记忆）
- 关于输出什么类型的结构化数据的指令

![image](https://github.com/user-attachments/assets/0f1f193f-8e94-4044-a276-576bd7764fd0)

### 关于上下文工程

本指南全部关于从今天的模型中获得尽可能多的收益。值得注意的是没有提到：

- 对模型参数的更改，如temperature、top_p、frequency_penalty、presence_penalty等
- 训练你自己的完成或嵌入模型
- 微调现有模型

再次，我不知道向LLM传递上下文的最佳方式是什么，但我知道你想要能够尝试一切的灵活性。

#### 标准 vs 自定义上下文格式

大多数LLM客户端使用这样的标准基于消息的格式：

```yaml
[
  {
    "role": "system",
    "content": "你是一个有用的助手..."
  },
  {
    "role": "user",
    "content": "你能部署后端吗？"
  },
  {
    "role": "assistant",
    "content": null,
    "tool_calls": [
      {
        "id": "1",
        "name": "list_git_tags",
        "arguments": "{}"
      }
    ]
  },
  {
    "role": "tool",
    "name": "list_git_tags",
    "content": "{\"tags\": [{\"name\": \"v1.2.3\", \"commit\": \"abc123\", \"date\": \"2024-03-15T10:00:00Z\"}, {\"name\": \"v1.2.2\", \"commit\": \"def456\", \"date\": \"2024-03-14T15:30:00Z\"}, {\"name\": \"v1.2.1\", \"commit\": \"abe033d\", \"date\": \"2024-03-13T09:15:00Z\"}]}",
    "tool_call_id": "1"
  }
]
```

虽然这对大多数用例都很有效，但如果你想真正从今天的LLM中获得最大收益，你需要以最节省令牌和注意力效率的方式将上下文传入LLM。

作为标准基于消息格式的替代方案，你可以构建针对你的用例优化的自己的上下文格式。例如，你可以使用自定义对象并将它们打包/展开到一个或多个用户、系统、助手或工具消息中，根据需要。

这是将整个上下文窗口放入单个用户消息的例子：
```yaml

[
  {
    "role": "system",
    "content": "你是一个有用的助手..."
  },
  {
    "role": "user",
    "content": |
            这是到目前为止发生的一切：
        
        <slack_message>
            From: @alex
            Channel: #deployments
            Text: 你能部署后端吗？
        </slack_message>
        
        <list_git_tags>
            intent: "list_git_tags"
        </list_git_tags>
        
        <list_git_tags_result>
            tags:
              - name: "v1.2.3"
                commit: "abc123"
                date: "2024-03-15T10:00:00Z"
              - name: "v1.2.2"
                commit: "def456"
                date: "2024-03-14T15:30:00Z"
              - name: "v1.2.1"
                commit: "ghi789"
                date: "2024-03-13T09:15:00Z"
        </list_git_tags_result>
        
        下一步是什么？
    }
]
```

模型可能通过你提供的工具模式推断你在问它`下一步是什么`，但将其纳入你的提示模板永远不会有害。

### 代码示例

我们可以用这样的东西构建这个：

```python

class Thread:
  events: List[Event]

class Event:
  # 可以只使用字符串，或者可以明确 - 由你决定
  type: Literal["list_git_tags", "deploy_backend", "deploy_frontend", "request_more_information", "done_for_now", "list_git_tags_result", "deploy_backend_result", "deploy_frontend_result", "request_more_information_result", "done_for_now_result", "error"]
  data: ListGitTags | DeployBackend | DeployFrontend | RequestMoreInformation |  
        ListGitTagsResult | DeployBackendResult | DeployFrontendResult | RequestMoreInformationResult | string

def event_to_prompt(event: Event) -> str:
    data = event.data if isinstance(event.data, str) \
           else stringifyToYaml(event.data)

    return f"<{event.type}>\n{data}\n</{event.type}>"


def thread_to_prompt(thread: Thread) -> str:
  return '\n\n'.join(event_to_prompt(event) for event in thread.events)
```

#### 示例上下文窗口

这是使用这种方法的上下文窗口可能的样子：

**初始Slack请求：**
```xml
<slack_message>
    From: @alex
    Channel: #deployments
    Text: 你能将最新的后端部署到生产环境吗？
</slack_message>
```

**列出Git标签后：**
```xml
<slack_message>
    From: @alex
    Channel: #deployments
    Text: 你能将最新的后端部署到生产环境吗？
    Thread: []
</slack_message>

<list_git_tags>
    intent: "list_git_tags"
</list_git_tags>

<list_git_tags_result>
    tags:
      - name: "v1.2.3"
        commit: "abc123"
        date: "2024-03-15T10:00:00Z"
      - name: "v1.2.2"
        commit: "def456"
        date: "2024-03-14T15:30:00Z"
      - name: "v1.2.1"
        commit: "ghi789"
        date: "2024-03-13T09:15:00Z"
</list_git_tags_result>
```

**错误和恢复后：**
```xml
<slack_message>
    From: @alex
    Channel: #deployments
    Text: 你能将最新的后端部署到生产环境吗？
    Thread: []
</slack_message>

<deploy_backend>
    intent: "deploy_backend"
    tag: "v1.2.3"
    environment: "production"
</deploy_backend>

<e>
    error running deploy_backend: 无法连接到部署服务
</e>

<request_more_information>
    intent: "request_more_information_from_human"
    question: "我在连接部署服务时遇到问题，你能提供更多详细信息和/或检查服务状态吗？"
</request_more_information>

<human_response>
    data:
      response: "我不确定发生了什么，你能检查最新工作流的状态吗？"
</human_response>
```

从这里你的下一步可能是：

```python
nextStep = await determine_next_step(thread_to_prompt(thread))
```

```python
{
  "intent": "get_workflow_status",
  "workflow_name": "tag_push_prod.yaml",
}
```

XML风格的格式只是一个例子 - 重点是你可以构建对你的应用程序有意义的自己的格式。如果你有灵活性来实验不同的上下文结构以及你存储什么与你传递给LLM什么，你会获得更好的质量。

拥有你的上下文窗口的主要好处：

1. **信息密度**：以最大化LLM理解的方式构建信息
2. **错误处理**：以帮助LLM恢复的格式包含错误信息。考虑在错误和失败调用解决后将其从上下文窗口中隐藏。
3. **安全性**：控制传递给LLM的信息，过滤敏感数据
4. **灵活性**：随着你了解什么对你的用例最有效而调整格式
5. **令牌效率**：为令牌效率和LLM理解优化上下文格式

上下文包括：提示、指令、RAG文档、历史、工具调用、记忆

记住：上下文窗口是你与LLM的主要接口。控制你如何构建和呈现信息可以显著改善你的代理性能。

例子 - 信息密度 - 相同消息，更少令牌：

![Loom Screenshot 2025-04-22 at 09 00 56](https://github.com/user-attachments/assets/5cf041c6-72da-4943-be8a-99c73162b12a)

### 不要只听我的

在12-factor agents发布大约2个月后，上下文工程开始成为一个相当流行的术语。

<a href="https://x.com/karpathy/status/1937902205765607626"><img width="378" alt="Screenshot 2025-06-25 at 4 11 45 PM" src="https://github.com/user-attachments/assets/97e6e667-c35f-4855-8233-af40f05d6bce" /></a> <a href="https://x.com/tobi/status/1935533422589399127"><img width="378" alt="Screenshot 2025-06-25 at 4 12 59 PM" src="https://github.com/user-attachments/assets/7e6f5738-0d38-4910-82d1-7f5785b82b99" /></a>

还有一个相当好的[上下文工程备忘单](https://x.com/lenadroid/status/1943685060785524824)来自[@lenadroid](https://x.com/lenadroid)，2025年7月。

<a href="https://x.com/lenadroid/status/1943685060785524824"><img width="256" alt="image" src="https://github.com/user-attachments/assets/cac88aa3-8faf-440b-9736-cab95a9de477" /></a>

这里的重复主题：我不知道什么是最佳方法，但我知道你想要能够尝试一切的灵活性。

[← 拥有你的提示](./factor-02-own-your-prompts.md) | [工具只是结构化输出 →](./factor-04-tools-are-structured-outputs.md)
