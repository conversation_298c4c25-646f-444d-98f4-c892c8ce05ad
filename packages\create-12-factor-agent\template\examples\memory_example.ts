// Example demonstrating save_memory functionality in 12-factor agents
// This follows the principles of the framework

import { Thread, agent<PERSON>oop } from "../src/agent";

async function runMemoryExample() {
    console.log("=== 12-Factor Agents Memory Example ===\n");

    // Example 1: Save user preference
    console.log("1. Saving user preference...");
    let thread = new Thread([
        {
            type: "user_input",
            data: "Please remember that I prefer to receive notifications via email rather than SMS, and I'm in the Pacific timezone."
        }
    ]);

    thread = await agent<PERSON>oop(thread);
    console.log("Response:", thread.lastEvent().data.message || thread.lastEvent().data);
    console.log("\n");

    // Example 2: Save important fact
    console.log("2. Saving important fact...");
    thread = new Thread([
        {
            type: "user_input", 
            data: "Remember that our company's fiscal year ends in March, not December."
        }
    ]);

    thread = await agent<PERSON>oop(thread);
    console.log("Response:", thread.lastEvent().data.message || thread.lastEvent().data);
    console.log("\n");

    // Example 3: Retrieve memories
    console.log("3. Retrieving memories about preferences...");
    thread = new Thread([
        {
            type: "user_input",
            data: "What do you remember about my notification preferences?"
        }
    ]);

    thread = await agentLoop(thread);
    console.log("Response:", thread.lastEvent().data.message || thread.lastEvent().data);
    console.log("\n");

    // Example 4: Complex interaction with memory and calculation
    console.log("4. Complex interaction with memory and calculation...");
    thread = new Thread([
        {
            type: "user_input",
            data: "Calculate 15 * 8 and remember this calculation for our quarterly report."
        }
    ]);

    thread = await agentLoop(thread);
    console.log("Response:", thread.lastEvent().data.message || thread.lastEvent().data);
    console.log("\n");

    // Example 5: Retrieve calculation memories
    console.log("5. Retrieving calculation memories...");
    thread = new Thread([
        {
            type: "user_input",
            data: "What calculations have we done for the quarterly report?"
        }
    ]);

    thread = await agentLoop(thread);
    console.log("Response:", thread.lastEvent().data.message || thread.lastEvent().data);
    console.log("\n");

    console.log("=== Memory Example Complete ===");
}

// Run the example
if (require.main === module) {
    runMemoryExample().catch(console.error);
}

export { runMemoryExample };
