[← 返回 README](../README_zh.md)

### 6. 通过简单API启动/暂停/恢复

代理只是程序，我们对如何启动、查询、恢复和停止它们有期望。

[![pause-resume animation](../img/165-pause-resume-animation.gif)](../img/165-pause-resume-animation.gif)

<details>
<summary><a href="../img/165-pause-resume-animation.gif">GIF版本</a></summary>

![pause-resume animation](../img/165-pause-resume-animation.gif)

</details>

用户、应用程序、管道和其他代理应该能够通过简单的API轻松启动代理。

代理及其编排确定性代码应该能够在需要长时间运行操作时暂停代理。

像webhooks这样的外部触发器应该使代理能够从它们停止的地方恢复，而无需与代理编排器深度集成。

与[因子5 - 统一执行状态和业务状态](./factor-05-unify-execution-state.md)和[因子8 - 拥有你的控制流](./factor-08-own-your-control-flow.md)密切相关，但可以独立实现。

**注意** - 通常AI编排器会允许暂停和恢复，但不是在工具选择和工具执行之间的时刻。另见[因子7 - 通过工具调用联系人类](./factor-07-contact-humans-with-tools.md)和[因子11 - 从任何地方触发，在用户所在的地方遇见他们](./factor-11-trigger-from-anywhere.md)。

[← 统一执行状态](./factor-05-unify-execution-state.md) | [通过工具联系人类 →](./factor-07-contact-humans-with-tools.md)
