[← 返回 README](../README_zh.md)

### 11. 从任何地方触发，在用户所在的地方遇见他们

如果你在等待[humanlayer](https://humanlayer.dev)的推介，你做到了。如果你在做[因子6 - 通过简单API启动/暂停/恢复](./factor-06-launch-pause-resume.md)和[因子7 - 通过工具调用联系人类](./factor-07-contact-humans-with-tools.md)，你已经准备好合并这个因子了。

![1b0-trigger-from-anywhere](../img/1b0-trigger-from-anywhere.png)

使用户能够从slack、电子邮件、短信或他们想要的任何其他渠道触发代理。使代理能够通过相同的渠道响应。

好处：

- **在用户所在的地方遇见他们**：这帮助你构建感觉像真正人类的AI应用程序，或至少是数字同事
- **外循环代理**：使代理能够被非人类触发，例如事件、cron、故障、其他任何东西。它们可能工作5、20、90分钟，但当它们到达关键点时，它们可以联系人类寻求帮助、反馈或批准
- **高风险工具**：如果你能够快速引入各种人类，你可以给代理访问更高风险操作的权限，如发送外部电子邮件、更新生产数据等。维护清晰的标准为你提供了[执行更大更好事情](./factor-10-small-focused-agents.md#如果llm变得更聪明怎么办)的代理的可审计性和信心

[← 小型专注代理](./factor-10-small-focused-agents.md) | [无状态归约器 →](./factor-12-stateless-reducer.md)
