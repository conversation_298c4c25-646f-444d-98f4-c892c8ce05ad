[← 返回 README](../README_zh.md)

### 9. 将错误压缩到上下文窗口

这个有点短但值得一提。代理的好处之一是"自愈" - 对于短任务，LLM可能调用失败的工具。好的LLM有相当好的机会读取错误消息或堆栈跟踪，并找出在后续工具调用中要更改什么。

大多数框架都实现了这一点，但你可以在不做其他11个因子的情况下只做这一点。这是一个例子：

```python
thread = {"events": [initial_message]}

while True:
  next_step = await determine_next_step(thread_to_prompt(thread))
  thread["events"].append({
    "type": next_step.intent,
    "data": next_step,
  })
  try:
    result = await handle_next_step(thread, next_step) # 我们的switch语句
  except Exception as e:
    # 如果我们得到错误，我们可以将其添加到上下文窗口并重试
    thread["events"].append({
      "type": 'error',
      "data": format_error(e),
    })
    # 循环，或在这里做其他任何事情来尝试恢复
```

你可能想要为特定工具调用实现errorCounter，以限制单个工具的~3次尝试，或对你的用例有意义的任何其他逻辑。

```python
consecutive_errors = 0

while True:

  # ... 现有代码 ...

  try:
    result = await handle_next_step(thread, next_step)
    thread["events"].append({
      "type": next_step.intent + '_result',
      data: result,
    })
    # 成功！重置错误计数器
    consecutive_errors = 0
  except Exception as e:
    consecutive_errors += 1
    if consecutive_errors < 3:
      # 做循环并重试
      thread["events"].append({
        "type": 'error',
        "data": format_error(e),
      })
    else:
      # 跳出循环，重置上下文窗口的部分，升级给人类，或你想做的任何其他事情
      break
  }
}
```

达到某个连续错误阈值可能是[升级给人类](./factor-07-contact-humans-with-tools.md)的好地方，无论是通过模型决策还是通过控制流的确定性接管。

[![195-factor-09-errors](../img/195-factor-09-errors.gif)](../img/195-factor-09-errors.gif)

<details>
<summary>[GIF版本](../img/195-factor-09-errors.gif)</summary>

![195-factor-09-errors](../img/195-factor-09-errors.gif)

</details>

好处：

1. **自愈**：LLM可以读取错误消息并找出在后续工具调用中要更改什么
2. **持久性**：即使一个工具调用失败，代理也可以继续运行

我确信你会发现如果你做得太多，你的代理会开始旋转并可能一遍又一遍地重复相同的错误。

这就是[因子8 - 拥有你的控制流](./factor-08-own-your-control-flow.md)和[因子3 - 拥有你的上下文构建](./factor-03-own-your-context-window.md)发挥作用的地方 - 你不需要只是将原始错误放回去，你可以完全重构它的表示方式，从上下文窗口中删除以前的事件，或你发现有效让代理回到正轨的任何确定性事情。

但防止错误旋转的第一方法是拥抱[因子10 - 小型、专注的代理](./factor-10-small-focused-agents.md)。

[← 拥有你的控制流](./factor-08-own-your-control-flow.md) | [小型专注代理 →](./factor-10-small-focused-agents.md)
