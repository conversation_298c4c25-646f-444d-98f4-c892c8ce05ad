[← 返回 README](../README_zh.md)

### 10. 小型、专注的代理

与其构建试图做所有事情的单体代理，不如构建做一件事并做好的小型、专注的代理。代理只是更大的、主要是确定性系统中的一个构建块。

![1a0-small-focused-agents](../img/1a0-small-focused-agents.png)

这里的关键洞察是关于LLM的限制：任务越大越复杂，需要的步骤就越多，这意味着更长的上下文窗口。随着上下文的增长，LLM更可能迷失或失去焦点。通过保持代理专注于特定领域，最多3-10步，也许最多20步，我们保持上下文窗口可管理，LLM性能高。

> #### 随着上下文的增长，LLM更可能迷失或失去焦点

小型、专注代理的好处：

1. **可管理的上下文**：更小的上下文窗口意味着更好的LLM性能
2. **明确的责任**：每个代理都有明确定义的范围和目的
3. **更好的可靠性**：在复杂工作流中迷失的机会更少
4. **更容易测试**：更简单地测试和验证特定功能
5. **改进的调试**：当问题发生时更容易识别和修复

### 如果LLM变得更聪明怎么办？

如果LLM变得足够聪明来处理100步以上的工作流，我们还需要这个吗？

简而言之，是的。随着代理和LLM的改进，它们**可能**自然扩展到能够处理更长的上下文窗口。这意味着处理更大DAG的更多部分。这种小型、专注的方法确保你今天就能获得结果，同时为随着LLM上下文窗口变得更可靠而慢慢扩展代理范围做准备。（如果你以前重构过大型确定性代码库，你现在可能在点头）。

[![gif](../img/1a5-agent-scope-grow.gif)](../img/1a5-agent-scope-grow.gif)

<details>
<summary><a href="../img/1a5-agent-scope-grow.gif">GIF版本</a></summary>
![gif](../img/1a5-agent-scope-grow.gif)
</details>

对代理的大小/范围有意识，并且只以允许你保持质量的方式增长，这里是关键。正如[构建NotebookLM的团队所说](https://open.substack.com/pub/swyx/p/notebooklm?selection=08e1187c-cfee-4c63-93c9-71216640a5f8&utm_campaign=post-share-selection&utm_medium=web)：

> 我觉得一致地，AI构建中最神奇的时刻对我来说是当我真的、真的、真的就在模型能力的边缘附近时

无论那个边界在哪里，如果你能找到那个边界并一致地做对，你就会构建神奇的体验。这里有许多护城河可以建立，但像往常一样，它们需要一些工程严谨性。

[← 压缩错误](./factor-09-compact-errors.md) | [从任何地方触发 →](./factor-11-trigger-from-anywhere.md)
