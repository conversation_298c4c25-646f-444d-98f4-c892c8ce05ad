# Memory Management in 12-Factor Agents

This guide demonstrates how to implement and use memory management functionality following the 12-Factor Agents principles.

## Overview

The memory system allows your agent to:
- Save important information for future conversations
- Retrieve relevant context from previous interactions
- Update and manage stored memories
- Categorize memories by type and importance

## 12-Factor Principles Applied

### Factor 1: Natural Language to Tool Calls
Users can naturally request memory operations:
- "Remember that I prefer email notifications"
- "What do you know about my preferences?"
- "Save this calculation for later"

### Factor 4: Tools are Structured Outputs
Memory operations are defined as structured tool calls:
```typescript
class SaveMemoryTool {
    intent "save_memory"
    memory_content string
    memory_type string  // 'fact', 'preference', 'context', 'instruction'
    importance_level int  // 1-10
    tags string[]
}
```

### Factor 5: Unify Execution State and Business State
Memory is stored persistently and integrated with the agent's execution flow.

### Factor 8: Own Your Control Flow
The agent decides when to save, retrieve, or update memories based on context.

## Memory Types

1. **fact**: Objective information (e.g., "Company fiscal year ends in March")
2. **preference**: User preferences (e.g., "Prefers email over SMS")
3. **context**: Situational information (e.g., "Working on Q4 report")
4. **instruction**: Behavioral guidelines (e.g., "Always ask for approval before refunds")

## Usage Examples

### Basic Memory Saving
```typescript
// User input: "Remember that I'm in Pacific timezone"
// Agent will call save_memory tool with:
{
  intent: "save_memory",
  memory_content: "User is in Pacific timezone",
  memory_type: "fact",
  importance_level: 7,
  tags: ["timezone", "user_info"]
}
```

### Memory Retrieval
```typescript
// User input: "What timezone am I in?"
// Agent will call retrieve_memory tool with:
{
  intent: "retrieve_memory",
  query: "timezone",
  memory_type: "fact"
}
```

### Complex Interactions
The agent can combine memory operations with other tools:
```typescript
// User: "Calculate 15 * 8 and remember it for the quarterly report"
// 1. Agent calls multiply tool
// 2. Agent calls save_memory tool to store the result
```

## Running the Example

```bash
# Install dependencies
npm install

# Generate BAML client
npx baml-cli generate

# Run the memory example
npx tsx examples/memory_example.ts
```

## Testing

Run the BAML tests to verify memory functionality:
```bash
npx baml-cli test
```

## File Structure

```
src/
├── memory.ts          # Memory store implementation
├── agent.ts           # Agent with memory tool handling
baml_src/
├── tool_memory.baml   # Memory tool definitions
├── agent.baml         # Updated agent with memory tools
examples/
├── memory_example.ts  # Usage examples
```

## Best Practices

1. **Categorize Appropriately**: Use the right memory type for each piece of information
2. **Set Importance Levels**: Higher importance memories are prioritized in retrieval
3. **Use Descriptive Tags**: Tags help with future retrieval and organization
4. **Regular Cleanup**: Implement memory cleanup for outdated information
5. **Privacy Considerations**: Be mindful of what information is stored

## Integration with Existing Systems

The memory system can be easily integrated with:
- Database backends (PostgreSQL, MongoDB)
- Vector databases for semantic search
- External knowledge management systems
- User profile systems

## Next Steps

- Implement semantic search using embeddings
- Add memory expiration and cleanup
- Integrate with external knowledge bases
- Add memory sharing between agent instances
