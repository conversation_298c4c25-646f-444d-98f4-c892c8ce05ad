

class AddTool {
    intent "add"
    a int | float
    b int | float
}

class SubtractTool {
    intent "subtract"
    a int | float
    b int | float
}

class MultiplyTool {
    intent "multiply"
    a int | float
    b int | float
}

class DivideTool {
    intent "divide"
    a int | float
    b int | float
}

// Memory management tool
class SaveMemoryTool {
    intent "save_memory"
    memory_content string @description("The important information to remember for future conversations")
    memory_type string @description("Type of memory: 'fact', 'preference', 'context', 'instruction'")
    importance_level int @description("Importance level from 1-10, where 10 is most important")
    tags string[] @description("Optional tags to categorize the memory")
}

